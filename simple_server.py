#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单VPN测试服务器
用于测试VPN客户端连接功能
"""

import socket
import threading
import time

class SimpleVPNServer:
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        
    def start(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            print(f"简单VPN服务器已启动: {self.host}:{self.port}")
            print("默认认证: admin/password")
            print("按Ctrl+C停止服务器")
            print("-" * 40)
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    print(f"客户端连接: {address}")
                    
                    # 在新线程中处理客户端
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error:
                    if self.running:
                        print("接受连接时出错")
                    break
                    
        except Exception as e:
            print(f"服务器启动失败: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        print("服务器已停止")
    
    def handle_client(self, client_socket, address):
        """处理客户端连接"""
        try:
            # 接收认证信息
            auth_data = client_socket.recv(1024).decode('utf-8')
            print(f"收到认证数据: {auth_data}")
            
            # 简单认证
            if auth_data == "admin:password":
                client_socket.send(b"OK")
                print(f"客户端 {address} 认证成功")
                
                # 处理客户端请求
                while self.running:
                    try:
                        data = client_socket.recv(1024)
                        if not data:
                            break
                        
                        message = data.decode('utf-8')
                        print(f"收到消息: {message}")
                        
                        if message == "PING":
                            client_socket.send(b"PONG")
                        else:
                            client_socket.send(b"OK")
                            
                    except socket.error:
                        break
            else:
                client_socket.send(b"FAILED")
                print(f"客户端 {address} 认证失败")
                
        except Exception as e:
            print(f"处理客户端 {address} 时出错: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
            print(f"客户端 {address} 已断开")

def main():
    print("简单VPN测试服务器")
    print("=" * 40)
    
    server = SimpleVPNServer()
    
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        server.stop()

if __name__ == "__main__":
    main()
