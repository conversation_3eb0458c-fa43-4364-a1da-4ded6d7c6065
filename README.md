# VPN客户端工具

一个功能完整的VPN客户端工具，支持图形界面操作，提供安全的网络连接功能。

## 功能特性

- 🖥️ **图形用户界面**: 基于Tkinter的直观GUI界面
- 🔐 **安全连接**: 支持加密的VPN连接
- 📝 **配置管理**: 支持多服务器配置的保存和管理
- 📊 **状态监控**: 实时显示连接状态和流量统计
- 🔄 **自动重连**: 支持连接断开后自动重连
- 💾 **配置加密**: 支持配置文件加密存储
- 🌐 **跨平台**: 支持Windows、Linux、macOS

## 系统要求

- Python 3.6 或更高版本
- 网络连接
- 管理员权限（某些功能需要）

## 快速开始

### Windows用户

1. 双击运行 `start_vpn.bat`
2. 脚本会自动检查Python环境和依赖包
3. 如果缺少依赖，会自动安装

### Linux/Mac用户

1. 给脚本添加执行权限：
   ```bash
   chmod +x start_vpn.sh
   ```

2. 运行启动脚本：
   ```bash
   ./start_vpn.sh
   ```

### 手动安装

1. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行VPN客户端：
   ```bash
   python vpn_client.py
   ```

## 使用说明

### 添加VPN服务器

1. 点击"添加服务器"按钮
2. 填写服务器信息：
   - 服务器名称：自定义名称
   - 服务器地址：VPN服务器IP或域名
   - 端口：VPN服务器端口
   - 用户名：登录用户名
   - 密码：登录密码
3. 点击"确定"保存

### 连接VPN

1. 在服务器列表中选择要连接的服务器
2. 点击"连接VPN"按钮
3. 等待连接建立
4. 连接成功后状态会显示为绿色

### 断开VPN

1. 点击"断开VPN"按钮
2. 等待连接断开完成

## 测试服务器

项目包含一个测试VPN服务器，用于测试客户端功能：

```bash
python start_server.py
```

默认认证信息：
- 用户名：admin
- 密码：password
- 端口：8888

## 文件结构

```
VPN客户端工具/
├── vpn_client.py          # 主程序文件
├── vpn_network.py         # 网络连接模块
├── vpn_config.py          # 配置管理模块
├── start_vpn.bat          # Windows启动脚本
├── start_vpn.sh           # Linux/Mac启动脚本
├── start_server.py        # 测试服务器
├── requirements.txt       # 依赖包列表
└── README.md             # 说明文档
```

## 配置文件

配置文件默认保存在 `vpn_config.json`，包含：

- 服务器列表
- 用户设置
- UI配置

支持配置文件加密，保护敏感信息。

## 安全说明

- 密码信息会被加密存储
- 支持配置文件整体加密
- 网络传输使用加密协议
- 建议在可信网络环境中使用

## 故障排除

### 常见问题

1. **Python未找到**
   - 确保已安装Python 3.6+
   - 检查Python是否添加到系统PATH

2. **依赖包安装失败**
   - 检查网络连接
   - 尝试使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`

3. **连接失败**
   - 检查服务器地址和端口
   - 确认用户名密码正确
   - 检查防火墙设置

4. **权限不足**
   - 以管理员身份运行程序
   - 检查网络接口访问权限

### 日志查看

程序运行时会在界面下方显示详细日志，包括：
- 连接状态变化
- 错误信息
- 网络统计

## 开发说明

### 模块说明

- `vpn_client.py`: 主程序，包含GUI界面和主要逻辑
- `vpn_network.py`: 网络连接模块，处理VPN协议和数据传输
- `vpn_config.py`: 配置管理模块，处理配置文件的读写和加密

### 扩展功能

可以通过修改相应模块来扩展功能：
- 添加新的VPN协议支持
- 增强加密算法
- 添加更多UI功能
- 集成其他网络工具

## 许可证

本项目仅供学习和研究使用，请遵守当地法律法规。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

---

**注意**: 请确保在合法合规的环境中使用本工具，遵守相关法律法规。
