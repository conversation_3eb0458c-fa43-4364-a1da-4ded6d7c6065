@echo off
chcp 65001 >nul
title 简化版VPN客户端

echo ========================================
echo         简化版VPN客户端工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检查Python环境...
python --version

:: 启动简化版VPN客户端（无需额外依赖）
echo.
echo 启动简化版VPN客户端...
echo 注意: 此版本无需安装额外依赖包
echo.
python vpn_simple.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
