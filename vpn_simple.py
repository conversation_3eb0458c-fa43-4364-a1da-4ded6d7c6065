#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版VPN客户端工具
无需额外依赖，可直接运行
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import socket
import threading
import json
import os
import time
from datetime import datetime

class SimpleVPNClient:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简化版VPN客户端")
        self.root.geometry("500x400")
        
        # 状态变量
        self.connected = False
        self.current_connection = None
        self.servers = self.load_servers()
        
        self.create_widgets()
        self.update_server_list()
        
    def create_widgets(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 服务器配置区域
        server_frame = ttk.LabelFrame(main_frame, text="服务器配置", padding="10")
        server_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 服务器地址
        ttk.Label(server_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W)
        self.host_var = tk.StringVar(value="localhost")
        ttk.Entry(server_frame, textvariable=self.host_var, width=20).grid(row=0, column=1, padx=5)
        
        # 端口
        ttk.Label(server_frame, text="端口:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.port_var = tk.StringVar(value="8888")
        ttk.Entry(server_frame, textvariable=self.port_var, width=10).grid(row=0, column=3, padx=5)
        
        # 用户名
        ttk.Label(server_frame, text="用户名:").grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        self.username_var = tk.StringVar(value="admin")
        ttk.Entry(server_frame, textvariable=self.username_var, width=20).grid(row=1, column=1, padx=5, pady=(5, 0))
        
        # 密码
        ttk.Label(server_frame, text="密码:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        self.password_var = tk.StringVar(value="password")
        ttk.Entry(server_frame, textvariable=self.password_var, show="*", width=15).grid(row=1, column=3, padx=5, pady=(5, 0))
        
        # 连接控制
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.connect_btn = ttk.Button(control_frame, text="连接VPN", command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_current_config).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(control_frame, text="测试连接", command=self.test_connection).pack(side=tk.LEFT, padx=(10, 0))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True)
        
        self.status_label = ttk.Label(status_frame, text="未连接", foreground="red")
        self.status_label.pack(pady=5)
        
        # 日志显示
        log_frame = ttk.Frame(status_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(log_frame, height=15)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.log("简化版VPN客户端已启动")
    
    def load_servers(self):
        """加载服务器配置"""
        config_file = "simple_vpn_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_servers(self):
        """保存服务器配置"""
        config_file = "simple_vpn_config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.servers, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.log(f"保存配置失败: {e}")
            return False
    
    def update_server_list(self):
        """更新服务器列表"""
        if self.servers:
            # 加载第一个服务器配置
            first_server = list(self.servers.values())[0]
            self.host_var.set(first_server.get('host', 'localhost'))
            self.port_var.set(str(first_server.get('port', 8888)))
            self.username_var.set(first_server.get('username', 'admin'))
            self.password_var.set(first_server.get('password', 'password'))
    
    def save_current_config(self):
        """保存当前配置"""
        config_name = f"{self.host_var.get()}:{self.port_var.get()}"
        self.servers[config_name] = {
            'host': self.host_var.get(),
            'port': int(self.port_var.get()),
            'username': self.username_var.get(),
            'password': self.password_var.get()
        }
        
        if self.save_servers():
            self.log(f"配置已保存: {config_name}")
            messagebox.showinfo("成功", "配置保存成功")
        else:
            messagebox.showerror("错误", "配置保存失败")
    
    def test_connection(self):
        """测试连接"""
        host = self.host_var.get()
        try:
            port = int(self.port_var.get())
        except ValueError:
            messagebox.showerror("错误", "端口必须是数字")
            return
        
        self.log(f"测试连接到 {host}:{port}...")
        
        def test_worker():
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    self.root.after(0, lambda: self.log("连接测试成功"))
                    self.root.after(0, lambda: messagebox.showinfo("测试结果", "连接测试成功"))
                else:
                    self.root.after(0, lambda: self.log("连接测试失败"))
                    self.root.after(0, lambda: messagebox.showerror("测试结果", "连接测试失败"))
            except Exception as e:
                self.root.after(0, lambda: self.log(f"连接测试出错: {e}"))
                self.root.after(0, lambda: messagebox.showerror("测试结果", f"连接测试出错: {e}"))
        
        threading.Thread(target=test_worker, daemon=True).start()
    
    def toggle_connection(self):
        """切换连接状态"""
        if self.connected:
            self.disconnect()
        else:
            self.connect()
    
    def connect(self):
        """连接VPN"""
        if self.connected:
            return
        
        host = self.host_var.get()
        try:
            port = int(self.port_var.get())
        except ValueError:
            messagebox.showerror("错误", "端口必须是数字")
            return
        
        username = self.username_var.get()
        password = self.password_var.get()
        
        if not all([host, username, password]):
            messagebox.showerror("错误", "请填写完整的连接信息")
            return
        
        self.log(f"正在连接到 {host}:{port}...")
        
        def connect_worker():
            try:
                # 创建连接
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((host, port))
                
                # 发送认证信息
                auth_data = f"{username}:{password}".encode('utf-8')
                sock.send(auth_data)
                
                # 接收响应
                response = sock.recv(1024).decode('utf-8')
                
                if "OK" in response:
                    self.current_connection = sock
                    self.connected = True
                    
                    self.root.after(0, self.update_connected_ui)
                    self.root.after(0, lambda: self.log("VPN连接成功"))
                    
                    # 启动心跳线程
                    threading.Thread(target=self.heartbeat_worker, daemon=True).start()
                else:
                    sock.close()
                    self.root.after(0, lambda: self.log("认证失败"))
                    self.root.after(0, lambda: messagebox.showerror("错误", "认证失败"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.log(f"连接失败: {e}"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"连接失败: {e}"))
        
        threading.Thread(target=connect_worker, daemon=True).start()
    
    def disconnect(self):
        """断开连接"""
        if not self.connected:
            return
        
        self.log("正在断开连接...")
        self.connected = False
        
        if self.current_connection:
            try:
                self.current_connection.close()
            except:
                pass
            self.current_connection = None
        
        self.update_disconnected_ui()
        self.log("连接已断开")
    
    def heartbeat_worker(self):
        """心跳线程"""
        while self.connected and self.current_connection:
            try:
                # 发送心跳包
                self.current_connection.send(b"PING")
                
                # 接收响应
                response = self.current_connection.recv(1024)
                if not response:
                    break
                
                self.root.after(0, lambda: self.log("心跳正常"))
                time.sleep(30)  # 30秒间隔
                
            except:
                break
        
        # 连接断开
        if self.connected:
            self.connected = False
            self.root.after(0, self.update_disconnected_ui)
            self.root.after(0, lambda: self.log("连接意外断开"))
    
    def update_connected_ui(self):
        """更新连接状态UI"""
        self.status_label.config(text="已连接", foreground="green")
        self.connect_btn.config(text="断开连接")
    
    def update_disconnected_ui(self):
        """更新断开状态UI"""
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接VPN")
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """程序关闭处理"""
        if self.connected:
            self.disconnect()
        self.root.destroy()


if __name__ == "__main__":
    try:
        app = SimpleVPNClient()
        app.run()
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")
