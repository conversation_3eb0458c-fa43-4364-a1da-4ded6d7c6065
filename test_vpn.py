#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN工具测试脚本
"""

import socket
import threading
import time

def test_server():
    """测试服务器"""
    print("启动测试服务器...")
    
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    try:
        server_socket.bind(('localhost', 8888))
        server_socket.listen(1)
        print("服务器已启动在 localhost:8888")
        print("等待客户端连接...")
        
        client_socket, address = server_socket.accept()
        print(f"客户端已连接: {address}")
        
        # 接收认证数据
        auth_data = client_socket.recv(1024).decode('utf-8')
        print(f"收到认证: {auth_data}")
        
        if auth_data == "admin:password":
            client_socket.send(b"OK")
            print("认证成功")
        else:
            client_socket.send(b"FAILED")
            print("认证失败")
        
        client_socket.close()
        
    except Exception as e:
        print(f"服务器错误: {e}")
    finally:
        server_socket.close()
        print("服务器已关闭")

def test_client():
    """测试客户端"""
    print("启动测试客户端...")
    time.sleep(1)  # 等待服务器启动
    
    try:
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 8888))
        print("已连接到服务器")
        
        # 发送认证数据
        client_socket.send(b"admin:password")
        
        # 接收响应
        response = client_socket.recv(1024).decode('utf-8')
        print(f"服务器响应: {response}")
        
        client_socket.close()
        
    except Exception as e:
        print(f"客户端错误: {e}")

def main():
    print("VPN工具连接测试")
    print("=" * 30)
    
    # 启动服务器线程
    server_thread = threading.Thread(target=test_server)
    server_thread.daemon = True
    server_thread.start()
    
    # 启动客户端线程
    client_thread = threading.Thread(target=test_client)
    client_thread.daemon = True
    client_thread.start()
    
    # 等待测试完成
    client_thread.join()
    time.sleep(1)
    
    print("测试完成")

if __name__ == "__main__":
    main()
