@echo off
chcp 65001 >nul
title VPN客户端工具

echo ========================================
echo           VPN客户端工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检查Python环境...
python --version

:: 检查并安装依赖
echo.
echo 检查依赖包...
pip show cryptography >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包已安装
)

:: 启动VPN客户端
echo.
echo 启动VPN客户端...
echo.
python vpn_client.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
