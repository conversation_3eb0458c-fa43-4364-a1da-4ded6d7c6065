#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN客户端工具
功能：连接VPN服务器，提供图形界面管理
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import socket
import threading
import subprocess
import json
import os
import time
import sys
from datetime import datetime

class VPNClient:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("VPN客户端工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 状态变量
        self.connected = False
        self.current_server = None
        self.connection_thread = None
        self.monitor_thread = None
        self.stop_monitor = False
        
        # 配置文件路径
        self.config_file = "vpn_config.json"
        self.servers = self.load_config()
        
        # 创建界面
        self.create_widgets()
        self.update_server_list()
        
    def create_widgets(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 服务器选择区域
        server_frame = ttk.LabelFrame(main_frame, text="VPN服务器", padding="10")
        server_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 服务器列表
        ttk.Label(server_frame, text="选择服务器:").grid(row=0, column=0, sticky=tk.W)
        self.server_var = tk.StringVar()
        self.server_combo = ttk.Combobox(server_frame, textvariable=self.server_var, 
                                        state="readonly", width=40)
        self.server_combo.grid(row=0, column=1, padx=(10, 0), sticky=(tk.W, tk.E))
        
        # 服务器管理按钮
        btn_frame = ttk.Frame(server_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(btn_frame, text="添加服务器", command=self.add_server).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="编辑服务器", command=self.edit_server).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="删除服务器", command=self.delete_server).pack(side=tk.LEFT)
        
        # 连接控制区域
        control_frame = ttk.LabelFrame(main_frame, text="连接控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="连接VPN", command=self.toggle_connection)
        self.connect_btn.pack(pady=5)
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(status_frame, text="未连接", foreground="red")
        self.status_label.pack(pady=5)
        
        # 详细信息文本框
        self.info_text = tk.Text(status_frame, height=15, width=70)
        scrollbar = ttk.Scrollbar(status_frame, orient="vertical", command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        server_frame.columnconfigure(1, weight=1)
        
        # 初始化日志
        self.log_message("VPN客户端已启动")
        
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.servers, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def update_server_list(self):
        """更新服务器列表"""
        server_names = list(self.servers.keys())
        self.server_combo['values'] = server_names
        if server_names and not self.server_var.get():
            self.server_var.set(server_names[0])
    
    def add_server(self):
        """添加服务器"""
        dialog = ServerDialog(self.root, "添加VPN服务器")
        if dialog.result:
            name, host, port, username, password = dialog.result
            if name in self.servers:
                messagebox.showerror("错误", "服务器名称已存在")
                return
            
            self.servers[name] = {
                'host': host,
                'port': int(port),
                'username': username,
                'password': password
            }
            self.save_config()
            self.update_server_list()
            self.server_var.set(name)
            self.log_message(f"已添加服务器: {name}")
    
    def edit_server(self):
        """编辑服务器"""
        current = self.server_var.get()
        if not current or current not in self.servers:
            messagebox.showwarning("警告", "请先选择一个服务器")
            return
        
        server_info = self.servers[current]
        dialog = ServerDialog(self.root, "编辑VPN服务器", 
                            (current, server_info['host'], server_info['port'],
                             server_info['username'], server_info['password']))
        
        if dialog.result:
            name, host, port, username, password = dialog.result
            if name != current and name in self.servers:
                messagebox.showerror("错误", "服务器名称已存在")
                return
            
            # 删除旧配置，添加新配置
            if name != current:
                del self.servers[current]
            
            self.servers[name] = {
                'host': host,
                'port': int(port),
                'username': username,
                'password': password
            }
            self.save_config()
            self.update_server_list()
            self.server_var.set(name)
            self.log_message(f"已更新服务器: {name}")
    
    def delete_server(self):
        """删除服务器"""
        current = self.server_var.get()
        if not current or current not in self.servers:
            messagebox.showwarning("警告", "请先选择一个服务器")
            return
        
        if messagebox.askyesno("确认", f"确定要删除服务器 '{current}' 吗？"):
            del self.servers[current]
            self.save_config()
            self.update_server_list()
            self.log_message(f"已删除服务器: {current}")
    
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.info_text.insert(tk.END, log_entry)
        self.info_text.see(tk.END)
        self.root.update_idletasks()
    
    def toggle_connection(self):
        """切换连接状态"""
        if self.connected:
            self.disconnect_vpn()
        else:
            self.connect_vpn()
    
    def connect_vpn(self):
        """连接VPN"""
        server_name = self.server_var.get()
        if not server_name or server_name not in self.servers:
            messagebox.showwarning("警告", "请先选择一个服务器")
            return
        
        if self.connected:
            messagebox.showinfo("提示", "已经连接到VPN")
            return
        
        self.current_server = self.servers[server_name]
        self.log_message(f"正在连接到服务器: {server_name}")
        
        # 在新线程中执行连接
        self.connection_thread = threading.Thread(target=self._connect_worker, args=(server_name,))
        self.connection_thread.daemon = True
        self.connection_thread.start()
    
    def _connect_worker(self, server_name):
        """连接工作线程"""
        try:
            server_info = self.current_server
            host = server_info['host']
            port = server_info['port']
            
            self.log_message(f"尝试连接到 {host}:{port}")
            
            # 模拟VPN连接过程
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            
            # 尝试连接
            sock.connect((host, port))
            self.log_message("TCP连接已建立")
            
            # 模拟认证过程
            auth_data = f"{server_info['username']}:{server_info['password']}"
            sock.send(auth_data.encode('utf-8'))
            
            response = sock.recv(1024).decode('utf-8')
            if "OK" in response:
                self.connected = True
                self.root.after(0, self._update_connected_ui, server_name)
                self.log_message("VPN连接成功！")
                
                # 启动监控线程
                self.stop_monitor = False
                self.monitor_thread = threading.Thread(target=self._monitor_connection, args=(sock,))
                self.monitor_thread.daemon = True
                self.monitor_thread.start()
            else:
                sock.close()
                self.root.after(0, self._update_disconnected_ui)
                self.log_message("认证失败")
                
        except Exception as e:
            self.root.after(0, self._update_disconnected_ui)
            self.log_message(f"连接失败: {e}")
    
    def _monitor_connection(self, sock):
        """监控连接状态"""
        try:
            while self.connected and not self.stop_monitor:
                # 发送心跳包
                try:
                    sock.send(b"PING")
                    response = sock.recv(1024)
                    if not response:
                        break
                    self.root.after(0, self.log_message, "连接正常")
                except:
                    break
                time.sleep(30)  # 30秒心跳间隔
        except:
            pass
        finally:
            try:
                sock.close()
            except:
                pass
            if self.connected:
                self.connected = False
                self.root.after(0, self._update_disconnected_ui)
                self.root.after(0, self.log_message, "连接已断开")
    
    def disconnect_vpn(self):
        """断开VPN连接"""
        if not self.connected:
            messagebox.showinfo("提示", "当前未连接VPN")
            return
        
        self.log_message("正在断开VPN连接...")
        self.stop_monitor = True
        self.connected = False
        self._update_disconnected_ui()
        self.log_message("VPN连接已断开")
    
    def _update_connected_ui(self, server_name):
        """更新连接状态UI"""
        self.status_label.config(text=f"已连接到: {server_name}", foreground="green")
        self.connect_btn.config(text="断开VPN")
    
    def _update_disconnected_ui(self):
        """更新断开状态UI"""
        self.status_label.config(text="未连接", foreground="red")
        self.connect_btn.config(text="连接VPN")
    
    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """程序关闭时的处理"""
        if self.connected:
            self.disconnect_vpn()
        self.root.destroy()


class ServerDialog:
    def __init__(self, parent, title, initial_data=None):
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # 创建表单
        self.create_form(initial_data)
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def create_form(self, initial_data):
        """创建表单"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 表单字段
        fields = [
            ("服务器名称:", "name"),
            ("服务器地址:", "host"),
            ("端口:", "port"),
            ("用户名:", "username"),
            ("密码:", "password")
        ]
        
        self.entries = {}
        
        for i, (label, key) in enumerate(fields):
            ttk.Label(main_frame, text=label).grid(row=i, column=0, sticky=tk.W, pady=5)
            
            if key == "password":
                entry = ttk.Entry(main_frame, show="*", width=30)
            else:
                entry = ttk.Entry(main_frame, width=30)
            
            entry.grid(row=i, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=5)
            self.entries[key] = entry
            
            # 填充初始数据
            if initial_data:
                if key == "name":
                    entry.insert(0, initial_data[0])
                elif key == "host":
                    entry.insert(0, initial_data[1])
                elif key == "port":
                    entry.insert(0, str(initial_data[2]))
                elif key == "username":
                    entry.insert(0, initial_data[3])
                elif key == "password":
                    entry.insert(0, initial_data[4])
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=len(fields), column=0, columnspan=2, pady=20)
        
        ttk.Button(btn_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
    
    def ok_clicked(self):
        """确定按钮点击"""
        # 验证输入
        name = self.entries["name"].get().strip()
        host = self.entries["host"].get().strip()
        port = self.entries["port"].get().strip()
        username = self.entries["username"].get().strip()
        password = self.entries["password"].get().strip()
        
        if not all([name, host, port, username, password]):
            messagebox.showerror("错误", "请填写所有字段")
            return
        
        try:
            port = int(port)
            if port < 1 or port > 65535:
                raise ValueError()
        except ValueError:
            messagebox.showerror("错误", "端口必须是1-65535之间的数字")
            return
        
        self.result = (name, host, port, username, password)
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()


if __name__ == "__main__":
    try:
        app = VPNClient()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")
