#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN网络连接模块
提供VPN连接、数据传输、路由管理等功能
"""

import socket
import threading
import time
import struct
import subprocess
import platform
import os
import select
from datetime import datetime

class VPNConnection:
    def __init__(self, server_info, callback=None):
        self.server_info = server_info
        self.callback = callback  # 状态回调函数
        self.connected = False
        self.socket = None
        self.tunnel_thread = None
        self.stop_flag = False
        
        # 统计信息
        self.bytes_sent = 0
        self.bytes_received = 0
        self.start_time = None
        
    def connect(self):
        """建立VPN连接"""
        try:
            self._log("开始建立VPN连接...")
            
            # 创建TCP连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            
            host = self.server_info['host']
            port = self.server_info['port']
            
            self._log(f"连接到服务器 {host}:{port}")
            self.socket.connect((host, port))
            
            # 发送认证信息
            auth_packet = self._create_auth_packet()
            self.socket.send(auth_packet)
            
            # 接收认证响应
            response = self.socket.recv(1024)
            if not self._verify_auth_response(response):
                raise Exception("认证失败")
            
            self._log("认证成功")
            
            # 设置为非阻塞模式
            self.socket.setblocking(False)
            
            # 启动隧道线程
            self.connected = True
            self.start_time = datetime.now()
            self.stop_flag = False
            
            self.tunnel_thread = threading.Thread(target=self._tunnel_worker)
            self.tunnel_thread.daemon = True
            self.tunnel_thread.start()
            
            self._log("VPN隧道已建立")
            return True
            
        except Exception as e:
            self._log(f"连接失败: {e}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """断开VPN连接"""
        self._log("正在断开VPN连接...")
        
        self.connected = False
        self.stop_flag = True
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        
        if self.tunnel_thread and self.tunnel_thread.is_alive():
            self.tunnel_thread.join(timeout=2)
        
        self._log("VPN连接已断开")
    
    def _create_auth_packet(self):
        """创建认证数据包"""
        username = self.server_info['username'].encode('utf-8')
        password = self.server_info['password'].encode('utf-8')
        
        # 简单的认证协议: [长度][用户名][长度][密码]
        packet = struct.pack('!H', len(username)) + username
        packet += struct.pack('!H', len(password)) + password
        
        return packet
    
    def _verify_auth_response(self, response):
        """验证认证响应"""
        if len(response) < 4:
            return False
        
        # 简单的响应格式: [状态码][消息长度][消息]
        status = struct.unpack('!H', response[:2])[0]
        return status == 0  # 0表示成功
    
    def _tunnel_worker(self):
        """隧道工作线程"""
        self._log("隧道线程已启动")
        
        try:
            while self.connected and not self.stop_flag:
                # 使用select检查socket状态
                ready = select.select([self.socket], [], [], 1.0)
                
                if ready[0]:
                    try:
                        data = self.socket.recv(4096)
                        if not data:
                            break
                        
                        self.bytes_received += len(data)
                        self._process_received_data(data)
                        
                    except socket.error as e:
                        if e.errno != socket.EWOULDBLOCK:
                            self._log(f"接收数据错误: {e}")
                            break
                
                # 发送心跳包
                if int(time.time()) % 30 == 0:
                    self._send_heartbeat()
                
        except Exception as e:
            self._log(f"隧道线程错误: {e}")
        
        finally:
            self.connected = False
            self._log("隧道线程已退出")
    
    def _process_received_data(self, data):
        """处理接收到的数据"""
        # 这里可以实现数据解密、路由等功能
        # 目前只是简单的日志记录
        self._log(f"接收到数据: {len(data)} 字节")
    
    def _send_heartbeat(self):
        """发送心跳包"""
        try:
            heartbeat = struct.pack('!HH', 1, 0)  # 类型1=心跳, 长度0
            self.socket.send(heartbeat)
            self.bytes_sent += len(heartbeat)
        except:
            pass
    
    def send_data(self, data):
        """发送数据"""
        if not self.connected or not self.socket:
            return False
        
        try:
            # 添加数据包头
            packet = struct.pack('!HH', 2, len(data)) + data  # 类型2=数据
            self.socket.send(packet)
            self.bytes_sent += len(packet)
            return True
        except:
            return False
    
    def get_statistics(self):
        """获取连接统计信息"""
        if not self.start_time:
            return None
        
        duration = datetime.now() - self.start_time
        return {
            'connected': self.connected,
            'duration': str(duration).split('.')[0],  # 去掉微秒
            'bytes_sent': self.bytes_sent,
            'bytes_received': self.bytes_received,
            'server': f"{self.server_info['host']}:{self.server_info['port']}"
        }
    
    def _log(self, message):
        """记录日志"""
        if self.callback:
            self.callback(message)


class VPNServer:
    """简单的VPN服务器实现（用于测试）"""
    
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = []
        self.running = False
        
    def start(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            print(f"VPN服务器已启动，监听 {self.host}:{self.port}")
            
            while self.running:
                try:
                    client_socket, address = self.server_socket.accept()
                    print(f"客户端连接: {address}")
                    
                    client_thread = threading.Thread(
                        target=self._handle_client, 
                        args=(client_socket, address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error:
                    if self.running:
                        print("接受连接时出错")
                    break
                    
        except Exception as e:
            print(f"服务器启动失败: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止服务器"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        print("VPN服务器已停止")
    
    def _handle_client(self, client_socket, address):
        """处理客户端连接"""
        try:
            # 接收认证信息
            auth_data = client_socket.recv(1024)
            if self._authenticate(auth_data):
                # 发送成功响应
                response = struct.pack('!HH', 0, 2) + b'OK'
                client_socket.send(response)
                print(f"客户端 {address} 认证成功")
                
                # 处理客户端数据
                while self.running:
                    try:
                        data = client_socket.recv(4096)
                        if not data:
                            break
                        
                        # 解析数据包
                        if len(data) >= 4:
                            packet_type, length = struct.unpack('!HH', data[:4])
                            
                            if packet_type == 1:  # 心跳包
                                # 回复心跳
                                heartbeat_response = struct.pack('!HH', 1, 2) + b'OK'
                                client_socket.send(heartbeat_response)
                            elif packet_type == 2:  # 数据包
                                # 处理数据（这里只是回显）
                                echo_response = struct.pack('!HH', 2, length) + data[4:4+length]
                                client_socket.send(echo_response)
                        
                    except socket.error:
                        break
            else:
                # 发送失败响应
                response = struct.pack('!HH', 1, 5) + b'FAILED'
                client_socket.send(response)
                print(f"客户端 {address} 认证失败")
                
        except Exception as e:
            print(f"处理客户端 {address} 时出错: {e}")
        finally:
            client_socket.close()
            print(f"客户端 {address} 已断开")
    
    def _authenticate(self, auth_data):
        """认证客户端"""
        try:
            if len(auth_data) < 4:
                return False
            
            # 解析用户名
            username_len = struct.unpack('!H', auth_data[:2])[0]
            username = auth_data[2:2+username_len].decode('utf-8')
            
            # 解析密码
            password_len = struct.unpack('!H', auth_data[2+username_len:4+username_len])[0]
            password = auth_data[4+username_len:4+username_len+password_len].decode('utf-8')
            
            # 简单的认证逻辑（实际应用中应该更安全）
            return username == 'admin' and password == 'password'
            
        except:
            return False


class NetworkUtils:
    """网络工具类"""
    
    @staticmethod
    def get_local_ip():
        """获取本地IP地址"""
        try:
            # 连接到一个远程地址来获取本地IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    @staticmethod
    def test_connection(host, port, timeout=5):
        """测试网络连接"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except:
            return False
    
    @staticmethod
    def get_network_interfaces():
        """获取网络接口信息"""
        interfaces = []
        try:
            if platform.system() == "Windows":
                # Windows系统
                result = subprocess.run(['ipconfig'], capture_output=True, text=True)
                # 解析ipconfig输出（简化版）
                interfaces.append("Windows网络接口")
            else:
                # Linux/Mac系统
                result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                # 解析ifconfig输出（简化版）
                interfaces.append("Unix网络接口")
        except:
            interfaces.append("无法获取网络接口信息")
        
        return interfaces


if __name__ == "__main__":
    # 测试服务器
    print("启动测试VPN服务器...")
    server = VPNServer()
    
    try:
        server.start()
    except KeyboardInterrupt:
        print("\n服务器被用户中断")
        server.stop()
