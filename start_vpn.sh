#!/bin/bash

# VPN客户端启动脚本 (Linux/Mac)

echo "========================================"
echo "           VPN客户端工具"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.6或更高版本"
    exit 1
fi

echo "检查Python环境..."
python3 --version

# 检查并安装依赖
echo
echo "检查依赖包..."
if ! python3 -c "import cryptography" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
else
    echo "依赖包已安装"
fi

# 启动VPN客户端
echo
echo "启动VPN客户端..."
echo
python3 vpn_client.py

if [ $? -ne 0 ]; then
    echo
    echo "程序运行出错，请检查错误信息"
    read -p "按回车键退出..."
fi
