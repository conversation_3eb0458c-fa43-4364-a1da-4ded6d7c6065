#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN测试服务器启动脚本
用于测试VPN客户端连接功能
"""

import sys
import threading
import time
from vpn_network import VPNServer

def main():
    print("VPN测试服务器")
    print("=" * 40)
    
    # 获取服务器配置
    host = input("服务器地址 (默认: localhost): ").strip() or "localhost"
    
    while True:
        port_input = input("服务器端口 (默认: 8888): ").strip() or "8888"
        try:
            port = int(port_input)
            if 1 <= port <= 65535:
                break
            else:
                print("端口号必须在1-65535之间")
        except ValueError:
            print("请输入有效的端口号")
    
    print(f"\n准备启动VPN服务器: {host}:{port}")
    print("默认认证信息:")
    print("  用户名: admin")
    print("  密码: password")
    print("\n按Ctrl+C停止服务器")
    print("-" * 40)
    
    # 创建并启动服务器
    server = VPNServer(host, port)
    
    try:
        # 在新线程中启动服务器
        server_thread = threading.Thread(target=server.start)
        server_thread.daemon = True
        server_thread.start()
        
        # 主线程等待用户中断
        while server.running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n正在停止服务器...")
        server.stop()
        print("服务器已停止")
    except Exception as e:
        print(f"\n服务器运行出错: {e}")
        server.stop()

if __name__ == "__main__":
    main()
