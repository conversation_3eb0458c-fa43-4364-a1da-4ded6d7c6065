#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN工具演示脚本
展示VPN客户端的主要功能
"""

import os
import sys
import time
import subprocess
import threading

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("                VPN客户端工具演示")
    print("=" * 60)
    print()

def print_menu():
    """打印菜单"""
    print("请选择要演示的功能：")
    print()
    print("1. 启动简化版VPN客户端（推荐）")
    print("2. 启动完整版VPN客户端")
    print("3. 启动测试服务器")
    print("4. 运行连接测试")
    print("5. 查看项目文件")
    print("6. 查看使用说明")
    print("0. 退出")
    print()

def start_simple_client():
    """启动简化版客户端"""
    print("启动简化版VPN客户端...")
    print("注意：此版本无需安装额外依赖包")
    print()
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['python', 'vpn_simple.py'])
        else:  # Linux/Mac
            subprocess.run(['python3', 'vpn_simple.py'])
    except Exception as e:
        print(f"启动失败: {e}")

def start_full_client():
    """启动完整版客户端"""
    print("启动完整版VPN客户端...")
    print("检查依赖包...")
    
    try:
        import cryptography
        print("依赖包已安装")
    except ImportError:
        print("正在安装依赖包...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        except Exception as e:
            print(f"依赖包安装失败: {e}")
            return
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['python', 'vpn_client.py'])
        else:  # Linux/Mac
            subprocess.run(['python3', 'vpn_client.py'])
    except Exception as e:
        print(f"启动失败: {e}")

def start_test_server():
    """启动测试服务器"""
    print("启动测试服务器...")
    print("服务器配置：")
    print("  地址: localhost")
    print("  端口: 8888")
    print("  用户名: admin")
    print("  密码: password")
    print()
    print("按Ctrl+C停止服务器")
    print("-" * 40)
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['python', 'simple_server.py'])
        else:  # Linux/Mac
            subprocess.run(['python3', 'simple_server.py'])
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")

def run_connection_test():
    """运行连接测试"""
    print("运行VPN连接测试...")
    print("测试服务器和客户端之间的连接...")
    print()
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['python', 'test_vpn.py'])
        else:  # Linux/Mac
            subprocess.run(['python3', 'test_vpn.py'])
    except Exception as e:
        print(f"测试失败: {e}")

def show_project_files():
    """显示项目文件"""
    print("项目文件结构：")
    print("-" * 40)
    
    files = [
        ("vpn_simple.py", "简化版VPN客户端（推荐）"),
        ("vpn_client.py", "完整版VPN客户端"),
        ("simple_server.py", "简单测试服务器"),
        ("test_vpn.py", "连接测试脚本"),
        ("start_simple.bat", "Windows简化版启动脚本"),
        ("start_vpn.bat", "Windows完整版启动脚本"),
        ("requirements.txt", "Python依赖包列表"),
        ("使用说明.md", "详细使用说明"),
        ("README.md", "项目说明文档")
    ]
    
    for filename, description in files:
        if os.path.exists(filename):
            print(f"✓ {filename:<20} - {description}")
        else:
            print(f"✗ {filename:<20} - {description} (文件不存在)")
    
    print()
    print("总计文件数:", len([f for f, _ in files if os.path.exists(f)]))

def show_usage():
    """显示使用说明"""
    print("VPN客户端工具使用说明")
    print("-" * 40)
    print()
    print("快速开始：")
    print("1. 首次使用建议选择'简化版VPN客户端'")
    print("2. 如需测试，先启动'测试服务器'")
    print("3. 在客户端中填写连接信息：")
    print("   - 服务器地址: localhost")
    print("   - 端口: 8888")
    print("   - 用户名: admin")
    print("   - 密码: password")
    print("4. 点击'测试连接'验证")
    print("5. 点击'连接VPN'建立连接")
    print()
    print("详细说明请查看 '使用说明.md' 文件")

def main():
    """主函数"""
    print_banner()
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项 (0-6): ").strip()
            print()
            
            if choice == '0':
                print("感谢使用VPN客户端工具！")
                break
            elif choice == '1':
                start_simple_client()
            elif choice == '2':
                start_full_client()
            elif choice == '3':
                start_test_server()
            elif choice == '4':
                run_connection_test()
            elif choice == '5':
                show_project_files()
            elif choice == '6':
                show_usage()
            else:
                print("无效选项，请重新选择")
            
            print()
            input("按回车键继续...")
            print()
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
