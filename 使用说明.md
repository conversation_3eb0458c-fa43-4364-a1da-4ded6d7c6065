# VPN客户端工具使用说明

## 🎯 项目概述

本项目提供了一个功能完整的VPN客户端工具，包含图形界面和完整的网络连接功能。支持多种使用方式，从简单到复杂满足不同需求。

## 📁 文件说明

### 主要程序文件
- `vpn_client.py` - 完整版VPN客户端（需要cryptography库）
- `vpn_simple.py` - 简化版VPN客户端（无需额外依赖）
- `vpn_network.py` - 网络连接模块
- `vpn_config.py` - 配置管理模块

### 测试服务器
- `simple_server.py` - 简单测试服务器
- `start_server.py` - 完整测试服务器
- `test_vpn.py` - 连接测试脚本

### 启动脚本
- `start_simple.bat` - Windows简化版启动脚本
- `start_vpn.bat` - Windows完整版启动脚本
- `start_vpn.sh` - Linux/Mac启动脚本

### 配置文件
- `requirements.txt` - Python依赖包列表
- `README.md` - 详细说明文档

## 🚀 快速开始

### 方法一：使用简化版（推荐新手）

1. **Windows用户**：
   ```
   双击运行 start_simple.bat
   ```

2. **Linux/Mac用户**：
   ```bash
   python3 vpn_simple.py
   ```

### 方法二：使用完整版

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **启动程序**：
   ```bash
   python vpn_client.py
   ```

## 🔧 使用步骤

### 1. 启动测试服务器（可选）

如果没有现成的VPN服务器，可以启动测试服务器：

```bash
python simple_server.py
```

默认配置：
- 地址：localhost
- 端口：8888
- 用户名：admin
- 密码：password

### 2. 配置VPN连接

在客户端界面中填写：
- **服务器地址**：VPN服务器的IP或域名
- **端口**：服务器端口号
- **用户名**：登录用户名
- **密码**：登录密码

### 3. 测试连接

点击"测试连接"按钮验证服务器是否可达。

### 4. 建立VPN连接

点击"连接VPN"按钮建立连接。连接成功后状态会显示为绿色。

### 5. 断开连接

点击"断开连接"按钮或关闭程序时会自动断开。

## 📊 功能特性

### 简化版功能
- ✅ 图形用户界面
- ✅ 基本VPN连接
- ✅ 连接状态监控
- ✅ 配置保存/加载
- ✅ 连接测试
- ✅ 实时日志显示

### 完整版额外功能
- ✅ 配置文件加密
- ✅ 多服务器管理
- ✅ 高级网络功能
- ✅ 详细统计信息
- ✅ 自动重连
- ✅ 配置导入/导出

## 🔍 故障排除

### 常见问题

1. **Python未找到**
   - 确保安装了Python 3.6+
   - 检查Python是否在系统PATH中

2. **连接失败**
   - 检查服务器地址和端口
   - 确认用户名密码正确
   - 检查网络连接和防火墙

3. **依赖包安装失败**
   - 使用国内镜像：`pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt`
   - 检查网络连接

4. **权限不足**
   - 以管理员身份运行
   - 检查防火墙设置

### 测试连接

运行连接测试脚本：
```bash
python test_vpn.py
```

## 📝 配置文件格式

简化版配置文件 `simple_vpn_config.json`：
```json
{
  "localhost:8888": {
    "host": "localhost",
    "port": 8888,
    "username": "admin",
    "password": "password"
  }
}
```

## 🔒 安全说明

- 配置文件中的密码会被保存，请确保文件安全
- 完整版支持配置文件加密
- 建议在可信网络环境中使用
- 定期更改VPN密码

## 🛠️ 开发说明

### 扩展功能

可以通过修改以下模块来扩展功能：

1. **添加新协议**：修改 `vpn_network.py`
2. **增强UI**：修改 `vpn_client.py` 或 `vpn_simple.py`
3. **配置管理**：修改 `vpn_config.py`

### 代码结构

```
VPN客户端
├── GUI界面层
├── 网络连接层
├── 配置管理层
└── 工具函数层
```

## 📞 技术支持

如遇到问题：

1. 查看程序日志输出
2. 检查网络连接
3. 确认配置信息
4. 运行测试脚本诊断

## ⚖️ 使用声明

- 本工具仅供学习和研究使用
- 请遵守当地法律法规
- 不得用于非法用途
- 使用者承担相应责任

---

**祝您使用愉快！** 🎉
