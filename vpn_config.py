#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VPN配置管理模块
提供配置文件的读取、保存、加密等功能
"""

import json
import os
import base64
import hashlib
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False
import getpass

class ConfigManager:
    def __init__(self, config_file="vpn_config.json", encrypted=True):
        self.config_file = config_file
        self.encrypted = encrypted
        self.config_data = {}
        self.encryption_key = None
        
        # 默认配置
        self.default_config = {
            "servers": {},
            "settings": {
                "auto_connect": False,
                "remember_password": True,
                "log_level": "INFO",
                "connection_timeout": 30,
                "heartbeat_interval": 30,
                "max_retry_attempts": 3
            },
            "ui_settings": {
                "window_size": "600x500",
                "theme": "default",
                "language": "zh_CN"
            }
        }
        
        self.load_config()
    
    def _get_encryption_key(self, password=None):
        """获取加密密钥"""
        if self.encryption_key:
            return self.encryption_key
        
        if not password:
            password = getpass.getpass("请输入配置文件密码: ").encode()
        else:
            password = password.encode()
        
        # 使用固定的盐值（实际应用中应该随机生成并保存）
        salt = b'vpn_client_salt_2023'
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password))
        self.encryption_key = key
        return key
    
    def _encrypt_data(self, data, password=None):
        """加密数据"""
        if not self.encrypted or not CRYPTO_AVAILABLE:
            return data

        try:
            key = self._get_encryption_key(password)
            f = Fernet(key)

            json_str = json.dumps(data, ensure_ascii=False, indent=2)
            encrypted_data = f.encrypt(json_str.encode('utf-8'))

            return {
                "encrypted": True,
                "data": base64.b64encode(encrypted_data).decode('ascii')
            }
        except Exception as e:
            print(f"加密失败: {e}")
            return data
    
    def _decrypt_data(self, encrypted_data, password=None):
        """解密数据"""
        if not isinstance(encrypted_data, dict) or not encrypted_data.get("encrypted"):
            return encrypted_data

        if not CRYPTO_AVAILABLE:
            print("警告: 加密库不可用，无法解密配置文件")
            return self.default_config

        try:
            key = self._get_encryption_key(password)
            f = Fernet(key)

            encrypted_bytes = base64.b64decode(encrypted_data["data"])
            decrypted_bytes = f.decrypt(encrypted_bytes)

            return json.loads(decrypted_bytes.decode('utf-8'))
        except Exception as e:
            print(f"解密失败: {e}")
            return self.default_config
    
    def load_config(self, password=None):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.config_data = self.default_config.copy()
            self.save_config()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            self.config_data = self._decrypt_data(raw_data, password)
            
            # 合并默认配置（处理新增的配置项）
            self._merge_default_config()
            
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config_data = self.default_config.copy()
    
    def save_config(self, password=None):
        """保存配置文件"""
        try:
            # 创建备份
            if os.path.exists(self.config_file):
                backup_file = self.config_file + ".backup"
                with open(self.config_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新配置
            data_to_save = self._encrypt_data(self.config_data, password)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def _merge_default_config(self):
        """合并默认配置"""
        def merge_dict(default, current):
            for key, value in default.items():
                if key not in current:
                    current[key] = value
                elif isinstance(value, dict) and isinstance(current[key], dict):
                    merge_dict(value, current[key])
        
        merge_dict(self.default_config, self.config_data)
    
    def get_servers(self):
        """获取服务器列表"""
        return self.config_data.get("servers", {})
    
    def add_server(self, name, server_info):
        """添加服务器"""
        if "servers" not in self.config_data:
            self.config_data["servers"] = {}
        
        self.config_data["servers"][name] = server_info
        return self.save_config()
    
    def update_server(self, old_name, new_name, server_info):
        """更新服务器信息"""
        servers = self.config_data.get("servers", {})
        
        if old_name in servers:
            del servers[old_name]
        
        servers[new_name] = server_info
        return self.save_config()
    
    def delete_server(self, name):
        """删除服务器"""
        servers = self.config_data.get("servers", {})
        if name in servers:
            del servers[name]
            return self.save_config()
        return False
    
    def get_server(self, name):
        """获取指定服务器信息"""
        return self.config_data.get("servers", {}).get(name)
    
    def get_setting(self, key, default=None):
        """获取设置项"""
        return self.config_data.get("settings", {}).get(key, default)
    
    def set_setting(self, key, value):
        """设置配置项"""
        if "settings" not in self.config_data:
            self.config_data["settings"] = {}
        
        self.config_data["settings"][key] = value
        return self.save_config()
    
    def get_ui_setting(self, key, default=None):
        """获取UI设置"""
        return self.config_data.get("ui_settings", {}).get(key, default)
    
    def set_ui_setting(self, key, value):
        """设置UI配置"""
        if "ui_settings" not in self.config_data:
            self.config_data["ui_settings"] = {}
        
        self.config_data["ui_settings"][key] = value
        return self.save_config()
    
    def export_config(self, export_file, include_passwords=False):
        """导出配置"""
        try:
            export_data = self.config_data.copy()
            
            if not include_passwords:
                # 移除密码信息
                servers = export_data.get("servers", {})
                for server_name, server_info in servers.items():
                    if "password" in server_info:
                        server_info["password"] = "***"
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file, merge=True):
        """导入配置"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if merge:
                # 合并配置
                if "servers" in import_data:
                    current_servers = self.config_data.get("servers", {})
                    current_servers.update(import_data["servers"])
                
                if "settings" in import_data:
                    current_settings = self.config_data.get("settings", {})
                    current_settings.update(import_data["settings"])
            else:
                # 替换配置
                self.config_data = import_data
            
            return self.save_config()
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def validate_server_config(self, server_info):
        """验证服务器配置"""
        required_fields = ["host", "port", "username", "password"]
        
        for field in required_fields:
            if field not in server_info:
                return False, f"缺少必需字段: {field}"
        
        # 验证端口号
        try:
            port = int(server_info["port"])
            if port < 1 or port > 65535:
                return False, "端口号必须在1-65535之间"
        except ValueError:
            return False, "端口号必须是数字"
        
        # 验证主机名
        host = server_info["host"].strip()
        if not host:
            return False, "主机名不能为空"
        
        # 验证用户名和密码
        if not server_info["username"].strip():
            return False, "用户名不能为空"
        
        if not server_info["password"]:
            return False, "密码不能为空"
        
        return True, "配置有效"
    
    def get_recent_servers(self, limit=5):
        """获取最近使用的服务器"""
        servers = self.config_data.get("servers", {})
        recent_list = []
        
        for name, info in servers.items():
            last_used = info.get("last_used", 0)
            recent_list.append((name, last_used))
        
        # 按最后使用时间排序
        recent_list.sort(key=lambda x: x[1], reverse=True)
        
        return [name for name, _ in recent_list[:limit]]
    
    def update_server_last_used(self, server_name):
        """更新服务器最后使用时间"""
        servers = self.config_data.get("servers", {})
        if server_name in servers:
            import time
            servers[server_name]["last_used"] = int(time.time())
            self.save_config()
    
    def reset_config(self):
        """重置配置为默认值"""
        self.config_data = self.default_config.copy()
        return self.save_config()


class ProfileManager:
    """配置文件管理器"""
    
    def __init__(self, profiles_dir="profiles"):
        self.profiles_dir = profiles_dir
        self.ensure_profiles_dir()
    
    def ensure_profiles_dir(self):
        """确保配置文件目录存在"""
        if not os.path.exists(self.profiles_dir):
            os.makedirs(self.profiles_dir)
    
    def list_profiles(self):
        """列出所有配置文件"""
        profiles = []
        try:
            for filename in os.listdir(self.profiles_dir):
                if filename.endswith('.json'):
                    profile_name = filename[:-5]  # 去掉.json后缀
                    profiles.append(profile_name)
        except:
            pass
        return profiles
    
    def create_profile(self, profile_name):
        """创建新的配置文件"""
        profile_path = os.path.join(self.profiles_dir, f"{profile_name}.json")
        
        if os.path.exists(profile_path):
            return False, "配置文件已存在"
        
        config_manager = ConfigManager(profile_path)
        if config_manager.save_config():
            return True, "配置文件创建成功"
        else:
            return False, "配置文件创建失败"
    
    def delete_profile(self, profile_name):
        """删除配置文件"""
        profile_path = os.path.join(self.profiles_dir, f"{profile_name}.json")
        
        try:
            if os.path.exists(profile_path):
                os.remove(profile_path)
                return True, "配置文件删除成功"
            else:
                return False, "配置文件不存在"
        except Exception as e:
            return False, f"删除失败: {e}"
    
    def load_profile(self, profile_name):
        """加载指定的配置文件"""
        profile_path = os.path.join(self.profiles_dir, f"{profile_name}.json")
        
        if not os.path.exists(profile_path):
            return None, "配置文件不存在"
        
        try:
            config_manager = ConfigManager(profile_path)
            return config_manager, "配置文件加载成功"
        except Exception as e:
            return None, f"加载失败: {e}"


if __name__ == "__main__":
    # 测试配置管理器
    config = ConfigManager("test_config.json", encrypted=False)
    
    # 添加测试服务器
    test_server = {
        "host": "test.vpn.com",
        "port": 1194,
        "username": "testuser",
        "password": "testpass"
    }
    
    config.add_server("测试服务器", test_server)
    
    # 测试配置读取
    servers = config.get_servers()
    print("服务器列表:", servers)
    
    # 测试设置
    config.set_setting("auto_connect", True)
    print("自动连接设置:", config.get_setting("auto_connect"))
    
    print("配置管理器测试完成")
